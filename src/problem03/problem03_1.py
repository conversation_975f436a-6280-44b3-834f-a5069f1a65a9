import pandas as pd
import pulp

# 加载数据
collect_points = pd.DataFrame({
    '编号': range(1, 31),
    'x': [12,5,20,25,35,18,30,10,22,38,5,15,28,30,10,20,35,8,25,32,15,28,38,10,20,30,5,18,35,22],
    'y': [8,15,30,10,22,5,35,25,18,15,8,32,5,12,10,20,30,22,25,8,5,20,25,30,10,18,25,30,10,35],
    '厨余': [0.72,1.38,1.08,1.55,1.62,1.76,0.77,1.02,1.32,1.45,1.35,1.87,2.58,1.134,0.78,0.768,0.72,1.595,1.5,1.08,0.912,0.9,0.99,1.44,1.74,1.17,1.7,2.64,0.864,0.986],
    '可回收': [0.12,0.23,0.18,0.31,0.27,0.384,0.168,0.238,0.176,0.3,0.27,0.51,0.516,0.21,0.13,0.192,0.27,0.348,0.36,0.18,0.19,0.195,0.27,0.24,0.319,0.39,0.34,0.66,0.216,0.204],
    '有害': [0.06,0.05,0.04,0.06,0.05,0.096,0.042,0.068,0.044,0.075,0.108,0.068,0.129,0.063,0.065,0.08,0.09,0.087,0.09,0.09,0.038,0.075,0.072,0.048,0.116,0.13,0.17,0.044,0.072,0.085],
    '其他': [0.3,0.64,0.5,1.18,0.76,0.96,0.42,0.374,0.66,0.675,0.972,0.952,1.075,0.693,0.325,0.56,0.72,0.87,1.05,0.45,0.76,0.33,0.468,0.672,0.725,0.91,1.19,1.056,0.648,0.425]
})

# 修正后的中转站数据，包含 x 和 y 坐标
transfer_stations = pd.DataFrame({
    '编号': [31, 32, 33, 34, 35],
    '建设成本(万元)': [500000, 600000, 450000, 550000, 580000],
    '存储容量': [
        [20, 15, 5, 30],
        [25, 20, 6, 35],
        [18, 12, 4, 25],
        [22, 18, 5, 32],
        [24, 22, 7, 38]
    ],
    'x': [15, 25, 35, 10, 20],  # 添加中转站的 x 坐标
    'y': [15, 25, 15, 25, 30]   # 添加中转站的 y 坐标
})

# 参数定义
num_collect_points = len(collect_points)
num_transfer_stations = len(transfer_stations)
garbage_types = ['厨余', '可回收', '有害', '其他']

# 将中转站的坐标和容量提取出来
transfer_station_coords = transfer_stations[['编号', 'x', 'y']]
transfer_station_costs = transfer_stations['建设成本(万元)'].values
transfer_station_capacities = transfer_stations['存储容量'].values

# 将建设成本转换为每日成本（假设使用10年，每年365天）
transfer_station_daily_costs = transfer_station_costs / (10 * 365)

# 运输成本参数（假设每公里运输成本为1元，可根据实际情况调整）
transport_cost_per_km = 1.0

# 计算收集点和中转站之间的欧氏距离
def euclidean_distance(x1, y1, x2, y2):
    return ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5

# 创建距离矩阵
distance_matrix = {}
for i in range(num_collect_points):
    cp_x = collect_points.iloc[i]['x']
    cp_y = collect_points.iloc[i]['y']
    for j in range(num_transfer_stations):
        ts_x = transfer_stations.iloc[j]['x']  # 现在可以正确访问 x 坐标
        ts_y = transfer_stations.iloc[j]['y']  # 现在可以正确访问 y 坐标
        distance_matrix[(i, j)] = euclidean_distance(cp_x, cp_y, ts_x, ts_y)

# 定义整数规划模型
model = pulp.LpProblem("Transfer_Station_Optimization", pulp.LpMinimize)

# 决策变量
z = pulp.LpVariable.dicts("Select_TS", list(range(num_transfer_stations)), cat='Binary')
x = pulp.LpVariable.dicts("Assign_CP_TO_TS",
                           [(i, j) for i in range(num_collect_points) for j in range(num_transfer_stations)],
                           cat='Binary')

# 目标函数：最小化总成本（中转站建设成本 + 运输成本）
model += (
    pulp.lpSum(transfer_station_daily_costs[j] * z[j] for j in range(num_transfer_stations)) +
    pulp.lpSum(transport_cost_per_km * distance_matrix[(i, j)] * x[(i, j)] for i in range(num_collect_points) for j in range(num_transfer_stations))
)

# 约束条件
# 1. 每个收集点必须分配到一个中转站
for i in range(num_collect_points):
    model += pulp.lpSum(x[(i, j)] for j in range(num_transfer_stations)) == 1

# 2. 如果收集点分配到中转站j，则中转站j必须被选中
for i in range(num_collect_points):
    for j in range(num_transfer_stations):
        model += x[(i, j)] <= z[j]

# 3. 中转站的垃圾总量不能超过其容量
for j in range(num_transfer_stations):
    for k in range(len(garbage_types)):  # 4种垃圾类型
        model += (
            pulp.lpSum(collect_points.iloc[i][garbage_types[k]] * x[(i, j)] for i in range(num_collect_points)) <=
            transfer_station_capacities[j][k]
        )

# 求解模型
model.solve()

# 输出结果
print("Optimal Solution Found!")
print(f"Total Cost: {pulp.value(model.objective):.2f} 元/日")

# 输出选中的中转站和分配的收集点
selected_transfer_stations = [j+1 for j in range(num_transfer_stations) if pulp.value(z[j]) == 1]
print(f"Selected Transfer Stations: {selected_transfer_stations}")

# 输出每个中转站的分配详情
for j in range(num_transfer_stations):
    if pulp.value(z[j]) == 1:
        assigned_points = [i+1 for i in range(num_collect_points) if pulp.value(x[(i, j)]) == 1]
        total_garbage = [0.0, 0.0, 0.0, 0.0]
        for i in assigned_points:
            i -= 1  # Convert to 0-based index
            for k in range(4):
                total_garbage[k] += collect_points.iloc[i][garbage_types[k]]
        print(f"\nTransfer Station {j+1} is selected (Cost: {transfer_station_daily_costs[j]:.2f} 元/日)")
        print(f"Assigned Collection Points: {assigned_points}")
        print(f"Total Garbage: 厨余={total_garbage[0]:.2f}, 可回收={total_garbage[1]:.2f}, 有害={total_garbage[2]:.2f}, 其他={total_garbage[3]:.2f}")