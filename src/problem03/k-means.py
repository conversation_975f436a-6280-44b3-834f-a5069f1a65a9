import os

import pandas as pd
from sklearn.cluster import KMeans

from __init__ import *

# 设置环境变量解决KMeans内存泄漏警告
os.environ["OMP_NUM_THREADS"] = "1"

# 加载收集点数据
collect_points = pd.DataFrame({
    '编号': range(1, 31),
    'x': [12,5,20,25,35,18,30,10,22,38,5,15,28,30,10,20,35,8,25,32,15,28,38,10,20,30,5,18,35,22],
    'y': [8,15,30,10,22,5,35,25,18,15,8,32,5,12,10,20,30,22,25,8,5,20,25,30,10,18,25,30,10,35],
    '厨余': [0.72,1.38,1.08,1.55,1.62,1.76,0.77,1.02,1.32,1.45,1.35,1.87,2.58,1.134,0.78,0.768,0.72,1.595,1.5,1.08,0.912,0.9,0.99,1.44,1.74,1.17,1.7,2.64,0.864,0.986],
    '可回收': [0.12,0.23,0.18,0.31,0.27,0.384,0.168,0.238,0.176,0.3,0.27,0.51,0.516,0.21,0.13,0.192,0.27,0.348,0.36,0.18,0.19,0.195,0.27,0.24,0.319,0.39,0.34,0.66,0.216,0.204],
    '有害': [0.06,0.05,0.04,0.06,0.05,0.096,0.042,0.068,0.044,0.075,0.108,0.068,0.129,0.063,0.065,0.08,0.09,0.087,0.09,0.09,0.038,0.075,0.072,0.048,0.116,0.13,0.17,0.044,0.072,0.085],
    '其他': [0.3,0.64,0.5,1.18,0.76,0.96,0.42,0.374,0.66,0.675,0.972,0.952,1.075,0.693,0.325,0.56,0.72,0.87,1.05,0.45,0.76,0.33,0.468,0.672,0.725,0.91,1.19,1.056,0.648,0.425]
})

# 加载候选中转站参数
transfer_stations = pd.DataFrame({
    '编号': [31,32,33,34,35],
    'x': [15,25,35,10,20],
    'y': [15,25,15,25,30],
    '建设成本(万元)': [50,60,45,55,58],
    '存储容量': [
        [20,15,5,30],  # 厨余、可回收、有害、其他
        [25,20,6,35],
        [18,12,4,25],
        [22,18,5,32],
        [24,22,7,38]
    ]
})

# 强制转换为浮点数类型
collect_points[['x', 'y']] = collect_points[['x', 'y']].astype(float)
transfer_stations[['x', 'y']] = transfer_stations[['x', 'y']].astype(float)

# 执行K-means聚类
kmeans = KMeans(n_clusters=5, init=transfer_stations[['x', 'y']].values, n_init=1, random_state=42)
kmeans.fit(collect_points[['x', 'y']].values)
collect_points['分配中转站'] = transfer_stations.iloc[kmeans.labels_]['编号'].values

# 输出详细的聚类分配结果
print("="*40 + "\n聚类分配详细信息：\n" + "="*40)
for station_id in transfer_stations['编号']:
    assigned_points = collect_points[collect_points['分配中转站'] == station_id]
    print(f"\n中转站 {station_id} 分配的收集点编号：")
    print(assigned_points['编号'].values)
    print(f"分配的垃圾总量（厨余、可回收、有害、其他）：")
    print(assigned_points[['厨余', '可回收', '有害', '其他']].sum().values.round(2))

# 可视化聚类结果
plt.figure(figsize=(12, 8), dpi=300)
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']  # 定义颜色方案

# 绘制收集点
for i, (station_id, color) in enumerate(zip(transfer_stations['编号'], colors)):
    cluster_data = collect_points[collect_points['分配中转站'] == station_id]
    plt.scatter(
        cluster_data['x'], cluster_data['y'],
        s=100, color=color, edgecolor='black',
        label=f'中转站 {station_id} ({len(cluster_data)}点)'
    )

# 绘制中转站位置
plt.scatter(
    transfer_stations['x'], transfer_stations['y'],
    s=300, marker='*', c=colors, edgecolor='black',
    linewidths=1.5, zorder=3, label='中转站中心'
)

# 添加标注
for idx, row in collect_points.iterrows():
    plt.text(row['x']+0.3, row['y']+0.3, str(int(row['编号'])), fontsize=8, ha='left')

# 图表装饰
plt.xlabel('X坐标', fontsize=12)
plt.ylabel('Y坐标', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.6)
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', borderaxespad=0.)
plt.tight_layout()

plt.show()